import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/app_export.dart';

/// An animated language toggle widget that provides immediate visual feedback
/// and delayed language change functionality.
///
/// This widget implements a two-phase animation system:
/// 1. Phase 1 (0ms): Immediate visual feedback with smooth animations
/// 2. Phase 2 (500ms delay): Actual language change via callback
///
/// Features:
/// - Smooth sliding selection indicator animation
/// - Haptic feedback on interaction
/// - Theme-aware colors matching existing design
/// - Timer-based delayed language change
/// - Proper cleanup and memory management
class AnimatedLanguageToggle extends StatefulWidget {
  /// Whether English is currently selected in the actual language state
  final bool isEnglish;
  
  /// Whether dark mode is currently active
  final bool isDarkMode;
  
  /// Callback function to trigger actual language change
  final Function(String) onLanguageChange;

  const AnimatedLanguageToggle({
    Key? key,
    required this.isEnglish,
    required this.isDarkMode,
    required this.onLanguageChange,
  }) : super(key: key);

  @override
  State<AnimatedLanguageToggle> createState() => _AnimatedLanguageToggleState();
}

class _AnimatedLanguageToggleState extends State<AnimatedLanguageToggle>
    with SingleTickerProviderStateMixin {
  
  /// Animation controller for smooth visual transitions
  late AnimationController _animationController;
  
  /// Animation for sliding selection indicator
  late Animation<double> _slideAnimation;
  
  /// Local visual state for immediate feedback (separate from actual language state)
  bool _visuallySelectedEnglish = true;
  
  /// Timer for delayed language change
  Timer? _languageChangeTimer;

  @override
  void initState() {
    super.initState();
    
    // Initialize visual state to match actual language state
    _visuallySelectedEnglish = widget.isEnglish;
    
    // Setup animation controller with 300ms duration
    _animationController = AnimationController(
      duration: AppAnimations.medium, // 300ms
      vsync: this,
    );
    
    // Create slide animation with easeInOut curve
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppAnimations.defaultCurve, // easeInOut
    ));
    
    // Set initial animation state
    if (!widget.isEnglish) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedLanguageToggle oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Sync visual state when actual language state changes from external sources
    if (oldWidget.isEnglish != widget.isEnglish) {
      setState(() {
        _visuallySelectedEnglish = widget.isEnglish;
      });
      
      // Update animation to match new state
      if (widget.isEnglish) {
        _animationController.reverse();
      } else {
        _animationController.forward();
      }
    }
  }

  @override
  void dispose() {
    // Clean up timer and animation controller
    _languageChangeTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  /// Handles language selection with immediate visual feedback and delayed change
  void _handleLanguageSelection(String languageCode) {
    // Provide immediate haptic feedback
    HapticFeedback.lightImpact();
    
    // Update visual state immediately for instant feedback
    setState(() {
      _visuallySelectedEnglish = languageCode == 'en';
    });
    
    // Trigger visual animation
    if (languageCode == 'en') {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    
    // Schedule delayed language change
    _scheduleLanguageChange(languageCode);
  }

  /// Schedules the actual language change after 500ms delay
  void _scheduleLanguageChange(String languageCode) {
    // Cancel any existing timer to prevent multiple language changes
    _languageChangeTimer?.cancel();
    
    // Schedule the actual language change after 500ms
    _languageChangeTimer = Timer(const Duration(milliseconds: 500), () {
      widget.onLanguageChange(languageCode);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Theme-aware colors matching existing design
    final backgroundColor = widget.isDarkMode
        ? const Color(0xFF2A2A2A) // SwiftUI dark languageToggleBackground
        : const Color(0xFFFAF8FF); // SwiftUI light languageToggleBackground

    final borderColor = widget.isDarkMode
        ? const Color(0xFF7E56D8).withValues(alpha: 0.3) // SwiftUI dark border
        : const Color(0xFF7E56D8).withValues(alpha: 0.2); // SwiftUI light border

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.h),
        border: Border.all(color: borderColor, width: 1.h),
      ),
      child: _buildAnimatedToggle(),
    );
  }

  /// Builds the animated toggle with sliding selection indicator
  Widget _buildAnimatedToggle() {
    return SizedBox(
      height: 24.h,
      child: Stack(
        children: [
          // Animated selection indicator
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return Positioned(
                left: _visuallySelectedEnglish ? 0 : 32.h,
                top: 0,
                child: AnimatedContainer(
                  duration: AppAnimations.medium,
                  curve: AppAnimations.defaultCurve,
                  width: 32.h,
                  height: 24.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF7E56D8), // selectedBackgroundColor
                    borderRadius: BorderRadius.circular(8.h),
                  ),
                ),
              );
            },
          ),
          // Language options
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLanguageOption(
                label: 'EN',
                isSelected: _visuallySelectedEnglish,
                onTap: () => _handleLanguageSelection('en'),
              ),
              _buildLanguageOption(
                label: 'عر',
                isSelected: !_visuallySelectedEnglish,
                onTap: () => _handleLanguageSelection('ar'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds individual language option with animated text colors
  Widget _buildLanguageOption({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // Theme-aware text colors
    final selectedTextColor = widget.isDarkMode
        ? const Color(0xFFE5E5E5) // SwiftUI dark toggleKnob
        : Colors.white; // SwiftUI light toggleKnob
    
    final unselectedTextColor = widget.isDarkMode
        ? const Color(0xFFE5E5E5).withValues(alpha: 0.75) // SwiftUI dark onSurface
        : const Color(0xFF161616).withValues(alpha: 0.75); // SwiftUI light onSurface

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32.h,
        height: 24.h,
        alignment: Alignment.center,
        child: AnimatedDefaultTextStyle(
          duration: AppAnimations.medium,
          curve: AppAnimations.defaultCurve,
          style: TextStyleHelper.instance.body12SemiBold.copyWith(
            color: isSelected ? selectedTextColor : unselectedTextColor,
          ),
          child: Text(label),
        ),
      ),
    );
  }
}
